#!/usr/bin/env python3
"""
提取指定脑区的时间序列数据
"""

import numpy as np
import pandas as pd
import os
import glob
from tqdm import tqdm

class TimeSeriesExtractor:
    def __init__(self, input_dir='node_features', output_dir='selected_timeseries'):
        """
        初始化时间序列提取器
        
        Parameters:
        -----------
        input_dir : str
            输入数据目录
        output_dir : str
            输出数据目录
        """
        self.input_dir = input_dir
        self.output_dir = output_dir
        
        # 指定要提取的脑区索引（从0开始计数）
        self.selected_regions = [7, 8, 23, 24, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 59, 60, 61, 62, 65, 66, 67, 68]
        
        # 加载脑区名称映射
        self.load_region_names()
        
    def load_region_names(self):
        """加载脑区名称映射"""
        try:
            df = pd.read_csv('aal_brain_regions.csv')
            self.region_names = dict(zip(df['Index'], df['Region_Name']))
            
            print("选择的脑区:")
            for idx in self.selected_regions:
                if idx in self.region_names:
                    print(f"  {idx}: {self.region_names[idx]}")
                else:
                    print(f"  {idx}: 未知脑区")
        except Exception as e:
            print(f"加载脑区名称时出错: {e}")
            self.region_names = {}
    
    def extract_subject_timeseries(self, input_file, output_file_txt, output_file_csv):
        """
        提取单个被试的指定脑区时间序列
        
        Parameters:
        -----------
        input_file : str
            输入文件路径
        output_file_txt : str
            输出TXT文件路径
        output_file_csv : str
            输出CSV文件路径
        """
        try:
            # 读取时间序列数据
            timeseries = np.loadtxt(input_file)
            
            # 检查数据维度
            if timeseries.shape[1] != 116:
                print(f"警告: 文件 {input_file} 的脑区数量不正确: {timeseries.shape[1]}")
                return False
            
            # 提取指定脑区的时间序列
            selected_timeseries = timeseries[:, self.selected_regions]
            
            # 保存为TXT文件
            np.savetxt(output_file_txt, selected_timeseries, fmt='%.6f', delimiter='\t')
            
            # 保存为CSV文件（带脑区名称）
            self.save_timeseries_with_labels(selected_timeseries, output_file_csv)
            
            return True
            
        except Exception as e:
            print(f"处理文件 {input_file} 时出错: {e}")
            return False
    
    def save_timeseries_with_labels(self, timeseries, csv_path):
        """
        保存带有脑区名称标签的时间序列为CSV文件
        
        Parameters:
        -----------
        timeseries : numpy.ndarray
            时间序列数据 (时间点 × 脑区)
        csv_path : str
            CSV文件保存路径
        """
        try:
            # 获取脑区名称
            region_names = []
            for idx in self.selected_regions:
                name = self.region_names.get(idx, f"Region_{idx}")
                region_names.append(name)
            
            # 创建DataFrame
            df = pd.DataFrame(timeseries, columns=region_names)
            
            # 添加时间点索引
            df.index.name = 'TimePoint'
            
            # 保存为CSV
            df.to_csv(csv_path, float_format='%.6f')
            
        except Exception as e:
            print(f"保存带标签的时间序列时出错: {e}")
    
    def extract_all_timeseries(self):
        """提取所有被试的时间序列"""
        print("=== 开始提取指定脑区的时间序列 ===")
        
        # 创建输出目录
        hc_output_dir = os.path.join(self.output_dir, 'HC')
        mci_output_dir = os.path.join(self.output_dir, 'MCI')
        os.makedirs(hc_output_dir, exist_ok=True)
        os.makedirs(mci_output_dir, exist_ok=True)
        
        # 处理HC组
        print("处理HC组...")
        hc_files = glob.glob(os.path.join(self.input_dir, 'HC', 'ROISignals_*.txt'))
        hc_success = 0
        
        for file_path in tqdm(hc_files, desc="HC组"):
            filename = os.path.splitext(os.path.basename(file_path))[0]
            
            output_txt = os.path.join(hc_output_dir, f"{filename}_selected.txt")
            output_csv = os.path.join(hc_output_dir, f"{filename}_selected.csv")
            
            if self.extract_subject_timeseries(file_path, output_txt, output_csv):
                hc_success += 1
        
        # 处理MCI组
        print("处理MCI组...")
        mci_files = glob.glob(os.path.join(self.input_dir, 'MCI', 'ROISignals_*.txt'))
        mci_success = 0
        
        for file_path in tqdm(mci_files, desc="MCI组"):
            filename = os.path.splitext(os.path.basename(file_path))[0]
            
            output_txt = os.path.join(mci_output_dir, f"{filename}_selected.txt")
            output_csv = os.path.join(mci_output_dir, f"{filename}_selected.csv")
            
            if self.extract_subject_timeseries(file_path, output_txt, output_csv):
                mci_success += 1
        
        # 输出统计信息
        print(f"\n=== 提取完成 ===")
        print(f"HC组: 成功处理 {hc_success}/{len(hc_files)} 个文件")
        print(f"MCI组: 成功处理 {mci_success}/{len(mci_files)} 个文件")
        print(f"总计: 成功处理 {hc_success + mci_success}/{len(hc_files) + len(mci_files)} 个文件")
        print(f"选定脑区数量: {len(self.selected_regions)}")
        print(f"输出目录: {self.output_dir}")
        
        # 保存选定脑区信息
        self.save_region_info()
    
    def save_region_info(self):
        """保存选定脑区的信息"""
        try:
            # 创建脑区信息DataFrame
            region_info = []
            for i, idx in enumerate(self.selected_regions):
                region_name = self.region_names.get(idx, f"Unknown_Region_{idx}")
                region_info.append({
                    'Column_Index': i,  # 在提取的数据中的列索引
                    'Original_Index': idx,  # 原始AAL索引
                    'Region_Name': region_name
                })
            
            df_regions = pd.DataFrame(region_info)
            df_regions.to_csv(os.path.join(self.output_dir, 'selected_regions_info.csv'), index=False)
            
            print(f"脑区信息已保存到: {os.path.join(self.output_dir, 'selected_regions_info.csv')}")
            
        except Exception as e:
            print(f"保存脑区信息时出错: {e}")

def main():
    """主函数"""
    print("=== 脑区时间序列提取工具 ===")
    
    # 创建提取器
    extractor = TimeSeriesExtractor()
    
    # 提取所有时间序列
    extractor.extract_all_timeseries()
    
    print("提取完成！")

if __name__ == "__main__":
    main()
