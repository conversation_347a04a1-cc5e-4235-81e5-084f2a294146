选取22个识别MCI的重要脑区进行数据可视化和gcn模型训练
seed=42
=== 总体性能 ===
总体准确率: 0.6633
HC组准确率: 0.8375 (283 个样本)
MCI组准确率: 0.4319 (213 个样本)

=== 各折次性能 ===
第1折准确率: 0.6566 (166 个样本)
第2折准确率: 0.7030 (165 个样本)
第3折准确率: 0.6303 (165 个样本)

=== 混淆矩阵分析 ===
混淆矩阵:
        预测
真实    HC   MCI
HC     237   46
MCI    121   92

=== 详细指标 ===
敏感性 (Sensitivity/Recall): 0.4319
特异性 (Specificity): 0.8375
精确率 (Precision): 0.6667
F1分数: 0.5242

seed=40
=== 总体性能 ===
总体准确率: 0.6673
HC组准确率: 0.9187 (283 个样本)
MCI组准确率: 0.3333 (213 个样本)

=== 各折次性能 ===
第1折准确率: 0.6687 (166 个样本)
第2折准确率: 0.7030 (165 个样本)
第3折准确率: 0.6303 (165 个样本)

=== 混淆矩阵分析 ===
混淆矩阵:
        预测
真实    HC   MCI
HC     260   23
MCI    142   71

=== 详细指标 ===
敏感性 (Sensitivity/Recall): 0.3333
特异性 (Specificity): 0.9187
精确率 (Precision): 0.7553
F1分数: 0.4625

seed=40,使用balanced方法增加MCI组的类别权重
=== 总体性能 ===
总体准确率: 0.6653
HC组准确率: 0.8587 (283 个样本)
MCI组准确率: 0.4085 (213 个样本)

=== 各折次性能 ===
第1折准确率: 0.6747 (166 个样本)
第2折准确率: 0.6970 (165 个样本)
第3折准确率: 0.6242 (165 个样本)

=== 混淆矩阵分析 ===
混淆矩阵:
        预测
真实    HC   MCI
HC     243   40
MCI    126   87

=== 详细指标 ===
敏感性 (Sensitivity/Recall): 0.4085
特异性 (Specificity): 0.8587
精确率 (Precision): 0.6850
F1分数: 0.5118