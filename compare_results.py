#!/usr/bin/env python3
"""
比较使用类别权重前后的GCN模型性能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def compare_performance():
    """比较使用类别权重前后的性能"""
    
    print("=== GCN模型性能对比分析 ===")
    print("比较使用类别权重前后的模型性能差异\n")
    
    # 手动输入之前的结果（无权重）
    results_without_weights = {
        'overall_accuracy': 0.6633,
        'hc_accuracy': 0.8375,
        'mci_accuracy': 0.4319,
        'sensitivity': 0.4319,
        'specificity': 0.8375,
        'precision': 0.6667,
        'f1_score': 0.5242,
        'fold_accuracies': [0.6566, 0.7030, 0.6303],
        'avg_accuracy': 0.6633,
        'std_accuracy': 0.0301
    }
    
    # 当前结果（有权重）
    try:
        results_df = pd.read_csv('subject_predictions.csv')
        
        # 计算当前结果的指标
        overall_accuracy = (results_df['true_label'] == results_df['predicted_label']).mean()
        
        hc_results = results_df[results_df['true_label'] == 0]
        mci_results = results_df[results_df['true_label'] == 1]
        
        hc_accuracy = (hc_results['true_label'] == hc_results['predicted_label']).mean()
        mci_accuracy = (mci_results['true_label'] == mci_results['predicted_label']).mean()
        
        # 计算各折次准确率
        fold_accuracies = []
        for fold in sorted(results_df['fold'].unique()):
            fold_data = results_df[results_df['fold'] == fold]
            fold_accuracy = (fold_data['true_label'] == fold_data['predicted_label']).mean()
            fold_accuracies.append(fold_accuracy)
        
        # 混淆矩阵指标
        from sklearn.metrics import confusion_matrix
        cm = confusion_matrix(results_df['true_label'], results_df['predicted_label'])
        tn, fp, fn, tp = cm.ravel()
        
        sensitivity = tp / (tp + fn)
        specificity = tn / (tn + fp)
        precision = tp / (tp + fp)
        f1_score = 2 * (precision * sensitivity) / (precision + sensitivity)
        
        results_with_weights = {
            'overall_accuracy': overall_accuracy,
            'hc_accuracy': hc_accuracy,
            'mci_accuracy': mci_accuracy,
            'sensitivity': sensitivity,
            'specificity': specificity,
            'precision': precision,
            'f1_score': f1_score,
            'fold_accuracies': fold_accuracies,
            'avg_accuracy': np.mean(fold_accuracies),
            'std_accuracy': np.std(fold_accuracies)
        }
        
    except FileNotFoundError:
        print("未找到当前预测结果文件")
        return
    
    # 创建对比表格
    comparison_data = {
        '指标': ['总体准确率', 'HC组准确率', 'MCI组准确率', '敏感性', '特异性', '精确率', 'F1分数', '平均准确率', '准确率标准差'],
        '无权重': [
            f"{results_without_weights['overall_accuracy']:.4f}",
            f"{results_without_weights['hc_accuracy']:.4f}",
            f"{results_without_weights['mci_accuracy']:.4f}",
            f"{results_without_weights['sensitivity']:.4f}",
            f"{results_without_weights['specificity']:.4f}",
            f"{results_without_weights['precision']:.4f}",
            f"{results_without_weights['f1_score']:.4f}",
            f"{results_without_weights['avg_accuracy']:.4f}",
            f"{results_without_weights['std_accuracy']:.4f}"
        ],
        '有权重': [
            f"{results_with_weights['overall_accuracy']:.4f}",
            f"{results_with_weights['hc_accuracy']:.4f}",
            f"{results_with_weights['mci_accuracy']:.4f}",
            f"{results_with_weights['sensitivity']:.4f}",
            f"{results_with_weights['specificity']:.4f}",
            f"{results_with_weights['precision']:.4f}",
            f"{results_with_weights['f1_score']:.4f}",
            f"{results_with_weights['avg_accuracy']:.4f}",
            f"{results_with_weights['std_accuracy']:.4f}"
        ]
    }
    
    # 计算改进幅度
    improvements = []
    for i, metric in enumerate(['overall_accuracy', 'hc_accuracy', 'mci_accuracy', 'sensitivity', 'specificity', 'precision', 'f1_score', 'avg_accuracy', 'std_accuracy']):
        if metric == 'std_accuracy':
            # 标准差越小越好
            improvement = (results_without_weights[metric] - results_with_weights[metric]) / results_without_weights[metric] * 100
        else:
            improvement = (results_with_weights[metric] - results_without_weights[metric]) / results_without_weights[metric] * 100
        improvements.append(f"{improvement:+.2f}%")
    
    comparison_data['改进幅度'] = improvements
    
    # 打印对比表格
    df_comparison = pd.DataFrame(comparison_data)
    print("=== 性能对比表 ===")
    print(df_comparison.to_string(index=False))
    
    # 保存对比表格
    df_comparison.to_csv('performance_comparison.csv', index=False)
    print(f"\n对比表格已保存到 performance_comparison.csv")
    
    # 绘制对比图表
    plot_comparison(results_without_weights, results_with_weights)
    
    # 分析结果
    print(f"\n=== 关键改进分析 ===")
    mci_improvement = (results_with_weights['mci_accuracy'] - results_without_weights['mci_accuracy']) / results_without_weights['mci_accuracy'] * 100
    sensitivity_improvement = (results_with_weights['sensitivity'] - results_without_weights['sensitivity']) / results_without_weights['sensitivity'] * 100
    
    print(f"✓ MCI组识别能力提升: {mci_improvement:+.2f}%")
    print(f"✓ 敏感性提升: {sensitivity_improvement:+.2f}%")
    print(f"✓ 模型稳定性: 标准差从 {results_without_weights['std_accuracy']:.4f} 变为 {results_with_weights['std_accuracy']:.4f}")
    
    if results_with_weights['mci_accuracy'] < results_without_weights['mci_accuracy']:
        print(f"⚠️  注意: MCI组准确率有所下降，可能需要调整权重策略")
    
    return df_comparison

def plot_comparison(results_without, results_with):
    """绘制性能对比图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 总体指标对比
    metrics = ['总体准确率', 'HC组准确率', 'MCI组准确率', 'F1分数']
    without_values = [results_without['overall_accuracy'], results_without['hc_accuracy'], 
                     results_without['mci_accuracy'], results_without['f1_score']]
    with_values = [results_with['overall_accuracy'], results_with['hc_accuracy'], 
                  results_with['mci_accuracy'], results_with['f1_score']]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    axes[0,0].bar(x - width/2, without_values, width, label='无权重', color='lightblue', alpha=0.8)
    axes[0,0].bar(x + width/2, with_values, width, label='有权重', color='lightcoral', alpha=0.8)
    axes[0,0].set_ylabel('准确率/F1分数')
    axes[0,0].set_title('主要性能指标对比')
    axes[0,0].set_xticks(x)
    axes[0,0].set_xticklabels(metrics, rotation=45)
    axes[0,0].legend()
    axes[0,0].set_ylim(0, 1)
    
    # 在柱子上添加数值
    for i, (v1, v2) in enumerate(zip(without_values, with_values)):
        axes[0,0].text(i - width/2, v1 + 0.01, f'{v1:.3f}', ha='center', va='bottom', fontsize=9)
        axes[0,0].text(i + width/2, v2 + 0.01, f'{v2:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 2. 各折次准确率对比
    folds = ['第1折', '第2折', '第3折']
    axes[0,1].plot(folds, results_without['fold_accuracies'], 'o-', label='无权重', linewidth=2, markersize=8)
    axes[0,1].plot(folds, results_with['fold_accuracies'], 's-', label='有权重', linewidth=2, markersize=8)
    axes[0,1].set_ylabel('准确率')
    axes[0,1].set_title('各折次准确率对比')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    axes[0,1].set_ylim(0.5, 0.8)
    
    # 3. 敏感性和特异性对比
    metrics_sens_spec = ['敏感性', '特异性']
    without_sens_spec = [results_without['sensitivity'], results_without['specificity']]
    with_sens_spec = [results_with['sensitivity'], results_with['specificity']]
    
    x = np.arange(len(metrics_sens_spec))
    axes[1,0].bar(x - width/2, without_sens_spec, width, label='无权重', color='lightgreen', alpha=0.8)
    axes[1,0].bar(x + width/2, with_sens_spec, width, label='有权重', color='orange', alpha=0.8)
    axes[1,0].set_ylabel('比率')
    axes[1,0].set_title('敏感性和特异性对比')
    axes[1,0].set_xticks(x)
    axes[1,0].set_xticklabels(metrics_sens_spec)
    axes[1,0].legend()
    axes[1,0].set_ylim(0, 1)
    
    # 在柱子上添加数值
    for i, (v1, v2) in enumerate(zip(without_sens_spec, with_sens_spec)):
        axes[1,0].text(i - width/2, v1 + 0.01, f'{v1:.3f}', ha='center', va='bottom', fontsize=10)
        axes[1,0].text(i + width/2, v2 + 0.01, f'{v2:.3f}', ha='center', va='bottom', fontsize=10)
    
    # 4. 改进幅度雷达图
    categories = ['总体准确率', 'MCI准确率', '敏感性', 'F1分数']
    improvements = [
        (results_with['overall_accuracy'] - results_without['overall_accuracy']) / results_without['overall_accuracy'] * 100,
        (results_with['mci_accuracy'] - results_without['mci_accuracy']) / results_without['mci_accuracy'] * 100,
        (results_with['sensitivity'] - results_without['sensitivity']) / results_without['sensitivity'] * 100,
        (results_with['f1_score'] - results_without['f1_score']) / results_without['f1_score'] * 100
    ]
    
    colors = ['green' if x > 0 else 'red' for x in improvements]
    bars = axes[1,1].bar(categories, improvements, color=colors, alpha=0.7)
    axes[1,1].set_ylabel('改进幅度 (%)')
    axes[1,1].set_title('性能改进幅度')
    axes[1,1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[1,1].tick_params(axis='x', rotation=45)
    
    # 在柱子上添加数值
    for bar, improvement in zip(bars, improvements):
        height = bar.get_height()
        axes[1,1].text(bar.get_x() + bar.get_width()/2., height + (1 if height > 0 else -3),
                      f'{improvement:+.1f}%', ha='center', va='bottom' if height > 0 else 'top', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("性能对比图表已保存到 performance_comparison.png")

if __name__ == "__main__":
    comparison_df = compare_performance()
    print("\n=== 总结 ===")
    print("类别权重的引入主要影响:")
    print("1. 提高了对少数类(MCI)的关注度")
    print("2. 在一定程度上平衡了敏感性和特异性")
    print("3. 可能需要进一步调优权重参数以获得最佳性能")
