#!/usr/bin/env python3
"""
检查提取的时间序列数据
"""

import os
import glob
import numpy as np
import pandas as pd

def check_timeseries():
    """检查提取的时间序列文件"""
    
    # 检查HC组
    hc_txt_files = glob.glob('selected_timeseries/HC/*_selected.txt')
    hc_csv_files = glob.glob('selected_timeseries/HC/*_selected.csv')
    
    # 检查MCI组
    mci_txt_files = glob.glob('selected_timeseries/MCI/*_selected.txt')
    mci_csv_files = glob.glob('selected_timeseries/MCI/*_selected.csv')
    
    print("=== 时间序列文件统计 ===")
    print(f"HC组:")
    print(f"  TXT文件数量: {len(hc_txt_files)}")
    print(f"  CSV文件数量: {len(hc_csv_files)}")
    
    print(f"MCI组:")
    print(f"  TXT文件数量: {len(mci_txt_files)}")
    print(f"  CSV文件数量: {len(mci_csv_files)}")
    
    print(f"总计:")
    print(f"  TXT文件数量: {len(hc_txt_files) + len(mci_txt_files)}")
    print(f"  CSV文件数量: {len(hc_csv_files) + len(mci_csv_files)}")
    
    # 检查时间序列维度
    if hc_txt_files:
        sample_timeseries = np.loadtxt(hc_txt_files[0])
        print(f"时间序列维度: {sample_timeseries.shape}")
        print(f"时间点数: {sample_timeseries.shape[0]}")
        print(f"脑区数: {sample_timeseries.shape[1]}")
        
        # 检查数据范围
        print(f"数据范围: [{np.min(sample_timeseries):.3f}, {np.max(sample_timeseries):.3f}]")
        print(f"数据均值: {np.mean(sample_timeseries):.3f}")
        print(f"数据标准差: {np.std(sample_timeseries):.3f}")
    
    # 检查CSV文件格式
    if hc_csv_files:
        sample_df = pd.read_csv(hc_csv_files[0], index_col=0)
        print(f"CSV文件列名: {list(sample_df.columns)}")
        print(f"CSV文件行数: {len(sample_df)}")
        print(f"CSV文件列数: {len(sample_df.columns)}")
    
    # 检查脑区信息文件
    if os.path.exists('selected_timeseries/selected_regions_info.csv'):
        region_info = pd.read_csv('selected_timeseries/selected_regions_info.csv')
        print(f"\n=== 脑区信息 ===")
        print(f"选定脑区数量: {len(region_info)}")
        print("脑区列表:")
        for _, row in region_info.iterrows():
            print(f"  列{row['Column_Index']}: {row['Region_Name']} (原索引{row['Original_Index']})")
    
    print("\n=== 示例文件路径 ===")
    if hc_txt_files:
        print(f"HC组TXT示例: {hc_txt_files[0]}")
    if hc_csv_files:
        print(f"HC组CSV示例: {hc_csv_files[0]}")
    if mci_txt_files:
        print(f"MCI组TXT示例: {mci_txt_files[0]}")
    if mci_csv_files:
        print(f"MCI组CSV示例: {mci_csv_files[0]}")
    
    # 验证数据一致性
    print("\n=== 数据一致性检查 ===")
    if hc_txt_files and hc_csv_files:
        # 检查TXT和CSV文件是否一致
        txt_data = np.loadtxt(hc_txt_files[0])
        csv_data = pd.read_csv(hc_csv_files[0], index_col=0).values
        
        if np.allclose(txt_data, csv_data, rtol=1e-5):
            print("✓ TXT和CSV文件数据一致")
        else:
            print("✗ TXT和CSV文件数据不一致")
    
    # 检查所有文件的维度一致性
    all_txt_files = hc_txt_files + mci_txt_files
    shapes = []
    for file in all_txt_files[:10]:  # 检查前10个文件
        try:
            data = np.loadtxt(file)
            shapes.append(data.shape)
        except:
            pass
    
    if len(set(shapes)) == 1:
        print("✓ 所有文件维度一致")
    else:
        print("✗ 文件维度不一致")
        print(f"发现的维度: {set(shapes)}")

if __name__ == "__main__":
    check_timeseries()
