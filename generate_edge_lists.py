import os
import numpy as np
import pandas as pd
import glob
from tqdm import tqdm

def load_region_names():
    """加载脑区名称映射"""
    try:
        df = pd.read_csv('aal_brain_regions.csv')
        region_names = dict(zip(df['Index'], df['Region_Name']))

        # 获取选定的脑区索引
        selected_regions = [7, 8, 23, 24, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 59, 60, 61, 62, 65, 66, 67, 68]

        # 创建新的索引映射（0-21对应选定的脑区）
        selected_region_names = {}
        for i, idx in enumerate(selected_regions):
            if idx in region_names:
                selected_region_names[i] = region_names[idx]
            else:
                selected_region_names[i] = f"Region_{idx}"

        return selected_region_names, selected_regions
    except Exception as e:
        print(f"加载脑区名称时出错: {e}")
        return {}, []

def matrix_to_edge_list(matrix, threshold=0.0):
    """
    将邻接矩阵转换为边列表
    
    参数:
    - matrix: 邻接矩阵 (N x N)
    - threshold: 考虑为连接的阈值，小于此值的连接会被忽略
    
    返回:
    - edge_list: 边列表，每行格式为 [源节点, 目标节点, 权重]
    """
    # 获取矩阵维度
    n = matrix.shape[0]
    edge_list = []
    
    # 遍历矩阵的上三角部分（避免重复边）
    for i in range(n):
        for j in range(i+1, n):
            weight = matrix[i, j]
            # 只保留权重大于阈值的边
            if abs(weight) > threshold:
                # 存储格式：源节点、目标节点、权重
                edge_list.append([i, j, weight])
    
    return np.array(edge_list)

def process_subject_files(file_paths, output_dir, threshold=0.0):
    """
    处理多个被试的矩阵文件，生成边列表

    参数:
    - file_paths: 矩阵文件路径列表
    - output_dir: 输出目录
    - threshold: 边权重阈值
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个文件
    for file_path in tqdm(file_paths, desc="处理被试文件"):
        # 提取被试ID
        filename = os.path.basename(file_path)
        # 从文件名中提取被试ID（去掉_submatrix.txt后缀）
        subject_id = filename.replace('_submatrix.txt', '')

        # 读取功能连接矩阵文件
        try:
            # 读取txt格式的功能连接矩阵
            matrix = np.loadtxt(file_path)

            # 检查矩阵是否为方阵
            if matrix.shape[0] != matrix.shape[1]:
                print(f"警告: 文件 {filename} 不是方阵: {matrix.shape}")
                continue
            
            # 将矩阵转换为边列表
            edge_list = matrix_to_edge_list(matrix, threshold=threshold)
            
            # 保存为CSV文件
            output_path = os.path.join(output_dir, f"{subject_id}_edges.csv")
            np.savetxt(output_path, edge_list, delimiter=',', fmt='%d,%d,%.6f',
                      header='source,target,weight', comments='')
            
            # 输出一些统计信息
            print(f"被试 {subject_id}: 生成了 {len(edge_list)} 条边")
            
            # 同时保存为适合GCN的数据格式（只有边列表，无权重）
            edge_index = edge_list[:, :2].astype(int)
            edge_attr = edge_list[:, 2].reshape(-1, 1)
            
            # 保存为numpy数组，适合PyTorch Geometric使用
            output_path_npz = os.path.join(output_dir, f"{subject_id}_gcn_data.npz")
            np.savez(output_path_npz, edge_index=edge_index, edge_attr=edge_attr)
            
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")

def main():
    # 设置参数
    threshold = 0.0  # 边权重阈值，小于此值的边将被忽略

    # 加载脑区名称信息
    region_names, selected_regions = load_region_names()

    print("=== 功能连接矩阵边列表生成工具 ===")
    print(f"选定脑区数量: {len(selected_regions)}")
    print("选定脑区列表:")
    for i, (new_idx, name) in enumerate(region_names.items()):
        original_idx = selected_regions[i] if i < len(selected_regions) else 'Unknown'
        print(f"  {new_idx}: {name} (原索引: {original_idx})")

    # 获取所有功能连接矩阵文件
    hc_files = sorted(glob.glob('subject_matrices/HC/*_submatrix.txt'))
    mci_files = sorted(glob.glob('subject_matrices/MCI/*_submatrix.txt'))

    print(f"\n找到 {len(hc_files)} 个HC被试和 {len(mci_files)} 个MCI被试的数据")
    
    # 处理HC组数据
    print("\n处理HC组数据...")
    process_subject_files(hc_files, 'edge_lists/HC', threshold)
    
    # 处理MCI组数据
    print("\n处理MCI组数据...")
    process_subject_files(mci_files, 'edge_lists/MCI', threshold)
    
    print("\n边列表生成完成！")
    
    # 生成一个汇总文件，记录每个被试属于哪个组（用于标签）
    labels = []

    # HC组标记为0
    for file_path in hc_files:
        filename = os.path.basename(file_path)
        subject_id = filename.replace('_submatrix.txt', '')
        labels.append((subject_id, 0))  # 0表示HC组

    # MCI组标记为1
    for file_path in mci_files:
        filename = os.path.basename(file_path)
        subject_id = filename.replace('_submatrix.txt', '')
        labels.append((subject_id, 1))  # 1表示MCI组
    
    # 保存标签文件
    os.makedirs('edge_lists', exist_ok=True)
    
    # 使用Python文件写入方式替代numpy.savetxt，避免格式问题
    with open('edge_lists/subject_labels.csv', 'w') as f:
        f.write('subject_id,label\n')  # 写入表头
        for subject_id, label in labels:
            f.write(f'{subject_id},{label}\n')
    
    print(f"生成了标签文件，包含 {len(labels)} 个被试的分组信息")

    # 保存脑区映射信息
    if region_names and selected_regions:
        region_mapping = []
        for i, (new_idx, name) in enumerate(region_names.items()):
            original_idx = selected_regions[i] if i < len(selected_regions) else -1
            region_mapping.append({
                'new_index': new_idx,
                'original_index': original_idx,
                'region_name': name
            })

        df_mapping = pd.DataFrame(region_mapping)
        df_mapping.to_csv('edge_lists/region_mapping.csv', index=False)
        print(f"生成了脑区映射文件: edge_lists/region_mapping.csv")

    # 生成一个简单的README文件，说明数据格式
    with open('edge_lists/README.txt', 'w', encoding='utf-8') as f:
        f.write("边列表数据说明\n")
        f.write("=============\n\n")
        f.write("数据来源: 从22个选定脑区的功能连接矩阵生成的边列表\n")
        f.write("脑区包括: 额叶、岛叶、扣带回、海马、顶叶等认知相关区域\n\n")
        f.write("每个被试的边列表文件包含以下格式：\n")
        f.write("1. CSV文件 (xxx_edges.csv): 源节点,目标节点,权重\n")
        f.write("   - 源节点/目标节点: 0-21 (对应22个选定脑区)\n")
        f.write("   - 权重: Fisher Z变换后的功能连接强度\n")
        f.write("2. NPZ文件 (xxx_gcn_data.npz): 包含两个数组：\n")
        f.write("   - edge_index: 形状为[E, 2]的数组，表示边的连接关系\n")
        f.write("   - edge_attr: 形状为[E, 1]的数组，表示边的权重\n\n")
        f.write("subject_labels.csv文件包含每个被试的组别信息：\n")
        f.write("- 0: HC (健康对照组)\n")
        f.write("- 1: MCI (轻度认知障碍组)\n\n")
        f.write("矩阵尺寸: 22 × 22 (对应22个选定脑区)\n")
        f.write("边数量: 最多231条边 (上三角矩阵，去除对角线)\n")

if __name__ == "__main__":
    main() 