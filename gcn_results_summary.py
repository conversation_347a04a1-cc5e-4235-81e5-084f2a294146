#!/usr/bin/env python3
"""
GCN模型结果总结
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report

def analyze_results():
    """分析GCN模型的结果"""
    
    print("=== GCN模型结果分析 ===")
    
    # 读取预测结果
    try:
        results_df = pd.read_csv('subject_predictions.csv')
        print(f"成功加载预测结果，包含 {len(results_df)} 个样本")
    except FileNotFoundError:
        print("未找到预测结果文件 subject_predictions.csv")
        return
    
    # 总体性能统计
    print(f"\n=== 总体性能 ===")
    overall_accuracy = (results_df['true_label'] == results_df['predicted_label']).mean()
    print(f"总体准确率: {overall_accuracy:.4f}")
    
    # 按组别统计
    hc_results = results_df[results_df['true_label'] == 0]
    mci_results = results_df[results_df['true_label'] == 1]
    
    hc_accuracy = (hc_results['true_label'] == hc_results['predicted_label']).mean()
    mci_accuracy = (mci_results['true_label'] == mci_results['predicted_label']).mean()
    
    print(f"HC组准确率: {hc_accuracy:.4f} ({len(hc_results)} 个样本)")
    print(f"MCI组准确率: {mci_accuracy:.4f} ({len(mci_results)} 个样本)")
    
    # 按折次统计
    print(f"\n=== 各折次性能 ===")
    for fold in sorted(results_df['fold'].unique()):
        fold_data = results_df[results_df['fold'] == fold]
        fold_accuracy = (fold_data['true_label'] == fold_data['predicted_label']).mean()
        print(f"第{fold}折准确率: {fold_accuracy:.4f} ({len(fold_data)} 个样本)")
    
    # 混淆矩阵分析
    print(f"\n=== 混淆矩阵分析 ===")
    cm = confusion_matrix(results_df['true_label'], results_df['predicted_label'])
    print("混淆矩阵:")
    print(f"        预测")
    print(f"真实    HC   MCI")
    print(f"HC    {cm[0,0]:4d} {cm[0,1]:4d}")
    print(f"MCI   {cm[1,0]:4d} {cm[1,1]:4d}")
    
    # 计算各种指标
    tn, fp, fn, tp = cm.ravel()
    
    sensitivity = tp / (tp + fn)  # 敏感性（召回率）
    specificity = tn / (tn + fp)  # 特异性
    precision = tp / (tp + fp)    # 精确率
    f1_score = 2 * (precision * sensitivity) / (precision + sensitivity)
    
    print(f"\n=== 详细指标 ===")
    print(f"敏感性 (Sensitivity/Recall): {sensitivity:.4f}")
    print(f"特异性 (Specificity): {specificity:.4f}")
    print(f"精确率 (Precision): {precision:.4f}")
    print(f"F1分数: {f1_score:.4f}")
    
    # 分类报告
    print(f"\n=== 分类报告 ===")
    print(classification_report(results_df['true_label'], results_df['predicted_label'], 
                              target_names=['HC', 'MCI']))
    
    # 错误分析
    print(f"\n=== 错误分析 ===")
    errors = results_df[results_df['true_label'] != results_df['predicted_label']]
    print(f"总错误数: {len(errors)}")
    
    # HC被错误分类为MCI的情况
    hc_to_mci = errors[(errors['true_label'] == 0) & (errors['predicted_label'] == 1)]
    print(f"HC被误分为MCI: {len(hc_to_mci)} 个")
    
    # MCI被错误分类为HC的情况
    mci_to_hc = errors[(errors['true_label'] == 1) & (errors['predicted_label'] == 0)]
    print(f"MCI被误分为HC: {len(mci_to_hc)} 个")
    
    # 保存错误分析结果
    if len(errors) > 0:
        errors.to_csv('prediction_errors.csv', index=False)
        print(f"错误分析结果已保存到 prediction_errors.csv")
    
    # 数据特征总结
    print(f"\n=== 数据特征总结 ===")
    print(f"节点特征维度: 6 (均值、标准差、最小值、最大值、偏度、峰度)")
    print(f"脑区数量: 22 个选定的认知相关脑区")
    print(f"边数量: 231 条 (完全连接图)")
    print(f"训练方式: 3折交叉验证")
    print(f"模型架构: 3层GCN + 全局平均池化 + 线性分类器")
    
    return results_df

def plot_performance_by_fold():
    """绘制各折次的性能对比"""
    try:
        results_df = pd.read_csv('subject_predictions.csv')
    except FileNotFoundError:
        print("未找到预测结果文件")
        return
    
    # 计算各折次的准确率
    fold_accuracies = []
    for fold in sorted(results_df['fold'].unique()):
        fold_data = results_df[results_df['fold'] == fold]
        accuracy = (fold_data['true_label'] == fold_data['predicted_label']).mean()
        fold_accuracies.append(accuracy)
    
    # 绘制柱状图
    plt.figure(figsize=(10, 6))
    
    plt.subplot(1, 2, 1)
    folds = [f'Fold {i}' for i in range(1, len(fold_accuracies) + 1)]
    bars = plt.bar(folds, fold_accuracies, color=['skyblue', 'lightgreen', 'lightcoral'])
    plt.ylabel('Accuracy')
    plt.title('Performance by Fold')
    plt.ylim(0, 1)
    
    # 在柱子上添加数值
    for bar, acc in zip(bars, fold_accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{acc:.3f}', ha='center', va='bottom')
    
    # 添加平均线
    mean_acc = np.mean(fold_accuracies)
    plt.axhline(y=mean_acc, color='red', linestyle='--', 
                label=f'Mean: {mean_acc:.3f}')
    plt.legend()
    
    # 绘制组别准确率对比
    plt.subplot(1, 2, 2)
    hc_acc = (results_df[results_df['true_label'] == 0]['true_label'] == 
              results_df[results_df['true_label'] == 0]['predicted_label']).mean()
    mci_acc = (results_df[results_df['true_label'] == 1]['true_label'] == 
               results_df[results_df['true_label'] == 1]['predicted_label']).mean()
    
    groups = ['HC', 'MCI']
    accuracies = [hc_acc, mci_acc]
    colors = ['lightblue', 'lightpink']
    
    bars = plt.bar(groups, accuracies, color=colors)
    plt.ylabel('Accuracy')
    plt.title('Performance by Group')
    plt.ylim(0, 1)
    
    # 在柱子上添加数值
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{acc:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('gcn_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("性能分析图已保存到 gcn_performance_analysis.png")

if __name__ == "__main__":
    # 分析结果
    results_df = analyze_results()
    
    # 绘制性能图
    if results_df is not None:
        plot_performance_by_fold()
    
    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print("- prediction_errors.csv: 错误分析详情")
    print("- gcn_performance_analysis.png: 性能分析图表")
    print("- overall_confusion_matrix.png: 总体混淆矩阵")
    print("- confusion_matrix_fold*.png: 各折次混淆矩阵")
