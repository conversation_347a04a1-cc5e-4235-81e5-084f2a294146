#!/usr/bin/env python3
"""
检查生成的子功能连接矩阵文件
"""

import os
import glob
import numpy as np
import pandas as pd

def check_submatrices():
    """检查生成的子矩阵文件"""
    
    # 检查HC组
    hc_txt_files = glob.glob('subject_matrices/HC/*_submatrix.txt')
    hc_csv_files = glob.glob('subject_matrices/HC/*_submatrix.csv')
    
    # 检查MCI组
    mci_txt_files = glob.glob('subject_matrices/MCI/*_submatrix.txt')
    mci_csv_files = glob.glob('subject_matrices/MCI/*_submatrix.csv')
    
    print("=== 子功能连接矩阵文件统计 ===")
    print(f"HC组:")
    print(f"  TXT文件数量: {len(hc_txt_files)}")
    print(f"  CSV文件数量: {len(hc_csv_files)}")
    
    print(f"MCI组:")
    print(f"  TXT文件数量: {len(mci_txt_files)}")
    print(f"  CSV文件数量: {len(mci_csv_files)}")
    
    print(f"总计:")
    print(f"  TXT文件数量: {len(hc_txt_files) + len(mci_txt_files)}")
    print(f"  CSV文件数量: {len(hc_csv_files) + len(mci_csv_files)}")
    
    # 检查矩阵尺寸
    if hc_txt_files:
        sample_matrix = np.loadtxt(hc_txt_files[0])
        print(f"矩阵尺寸: {sample_matrix.shape}")
        print(f"矩阵类型: 对称矩阵 (功能连接)")
        
        # 检查对角线是否为0
        diagonal_zeros = np.allclose(np.diag(sample_matrix), 0)
        print(f"对角线为0: {diagonal_zeros}")
        
        # 检查是否对称
        is_symmetric = np.allclose(sample_matrix, sample_matrix.T)
        print(f"矩阵对称: {is_symmetric}")
    
    # 检查CSV文件格式
    if hc_csv_files:
        sample_df = pd.read_csv(hc_csv_files[0], index_col=0)
        print(f"CSV文件列名: {list(sample_df.columns)[:5]}...")  # 显示前5个列名
        print(f"CSV文件行名: {list(sample_df.index)[:5]}...")    # 显示前5个行名
    
    print("\n=== 示例文件路径 ===")
    if hc_txt_files:
        print(f"HC组TXT示例: {hc_txt_files[0]}")
    if hc_csv_files:
        print(f"HC组CSV示例: {hc_csv_files[0]}")
    if mci_txt_files:
        print(f"MCI组TXT示例: {mci_txt_files[0]}")
    if mci_csv_files:
        print(f"MCI组CSV示例: {mci_csv_files[0]}")

if __name__ == "__main__":
    check_submatrices()
