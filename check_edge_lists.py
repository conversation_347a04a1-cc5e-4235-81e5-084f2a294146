#!/usr/bin/env python3
"""
检查生成的边列表数据
"""

import os
import glob
import numpy as np
import pandas as pd

def check_edge_lists():
    """检查生成的边列表文件"""
    
    # 检查HC组
    hc_csv_files = glob.glob('edge_lists/HC/*_edges.csv')
    hc_npz_files = glob.glob('edge_lists/HC/*_gcn_data.npz')
    
    # 检查MCI组
    mci_csv_files = glob.glob('edge_lists/MCI/*_edges.csv')
    mci_npz_files = glob.glob('edge_lists/MCI/*_gcn_data.npz')
    
    print("=== 边列表文件统计 ===")
    print(f"HC组:")
    print(f"  CSV文件数量: {len(hc_csv_files)}")
    print(f"  NPZ文件数量: {len(hc_npz_files)}")
    
    print(f"MCI组:")
    print(f"  CSV文件数量: {len(mci_csv_files)}")
    print(f"  NPZ文件数量: {len(mci_npz_files)}")
    
    print(f"总计:")
    print(f"  CSV文件数量: {len(hc_csv_files) + len(mci_csv_files)}")
    print(f"  NPZ文件数量: {len(hc_npz_files) + len(mci_npz_files)}")
    
    # 检查边列表格式
    if hc_csv_files:
        sample_edges = pd.read_csv(hc_csv_files[0])
        print(f"\n=== 边列表格式检查 ===")
        print(f"边数量: {len(sample_edges)}")
        print(f"列名: {list(sample_edges.columns)}")
        print(f"节点范围: {sample_edges['source'].min()}-{sample_edges['source'].max()}, {sample_edges['target'].min()}-{sample_edges['target'].max()}")
        print(f"权重范围: [{sample_edges['weight'].min():.3f}, {sample_edges['weight'].max():.3f}]")
        print(f"权重均值: {sample_edges['weight'].mean():.3f}")
        print(f"权重标准差: {sample_edges['weight'].std():.3f}")
    
    # 检查NPZ文件格式
    if hc_npz_files:
        sample_npz = np.load(hc_npz_files[0])
        print(f"\n=== NPZ文件格式检查 ===")
        print(f"包含的数组: {list(sample_npz.keys())}")
        if 'edge_index' in sample_npz:
            edge_index = sample_npz['edge_index']
            print(f"edge_index形状: {edge_index.shape}")
            print(f"edge_index数据类型: {edge_index.dtype}")
        if 'edge_attr' in sample_npz:
            edge_attr = sample_npz['edge_attr']
            print(f"edge_attr形状: {edge_attr.shape}")
            print(f"edge_attr数据类型: {edge_attr.dtype}")
    
    # 检查标签文件
    if os.path.exists('edge_lists/subject_labels.csv'):
        labels_df = pd.read_csv('edge_lists/subject_labels.csv')
        print(f"\n=== 标签文件检查 ===")
        print(f"总被试数: {len(labels_df)}")
        print(f"HC组数量: {len(labels_df[labels_df['label'] == 0])}")
        print(f"MCI组数量: {len(labels_df[labels_df['label'] == 1])}")
        print(f"标签分布: {labels_df['label'].value_counts().to_dict()}")
    
    # 检查脑区映射文件
    if os.path.exists('edge_lists/region_mapping.csv'):
        mapping_df = pd.read_csv('edge_lists/region_mapping.csv')
        print(f"\n=== 脑区映射检查 ===")
        print(f"脑区数量: {len(mapping_df)}")
        print("脑区映射:")
        for _, row in mapping_df.head(10).iterrows():
            print(f"  {row['new_index']}: {row['region_name']} (原索引: {row['original_index']})")
        if len(mapping_df) > 10:
            print(f"  ... 还有 {len(mapping_df) - 10} 个脑区")
    
    # 验证数据一致性
    print(f"\n=== 数据一致性检查 ===")
    
    # 检查所有边列表的边数是否一致
    edge_counts = []
    for file in hc_csv_files[:10] + mci_csv_files[:10]:  # 检查前10个文件
        try:
            df = pd.read_csv(file)
            edge_counts.append(len(df))
        except:
            pass
    
    if len(set(edge_counts)) == 1:
        print("✓ 所有文件的边数一致")
        print(f"  每个文件包含 {edge_counts[0]} 条边")
    else:
        print("✗ 文件边数不一致")
        print(f"  发现的边数: {set(edge_counts)}")
    
    # 检查CSV和NPZ文件数量是否匹配
    if len(hc_csv_files) == len(hc_npz_files) and len(mci_csv_files) == len(mci_npz_files):
        print("✓ CSV和NPZ文件数量匹配")
    else:
        print("✗ CSV和NPZ文件数量不匹配")
    
    # 检查边的完整性（是否包含所有可能的边）
    expected_edges = 22 * (22 - 1) // 2  # 上三角矩阵，去除对角线
    if edge_counts and edge_counts[0] == expected_edges:
        print(f"✓ 边数正确: {expected_edges} 条边（22个节点的完全图）")
    else:
        print(f"✗ 边数异常: 期望 {expected_edges} 条边，实际 {edge_counts[0] if edge_counts else 0} 条边")
    
    print(f"\n=== 示例文件路径 ===")
    if hc_csv_files:
        print(f"HC组CSV示例: {hc_csv_files[0]}")
    if hc_npz_files:
        print(f"HC组NPZ示例: {hc_npz_files[0]}")
    if mci_csv_files:
        print(f"MCI组CSV示例: {mci_csv_files[0]}")
    if mci_npz_files:
        print(f"MCI组NPZ示例: {mci_npz_files[0]}")

if __name__ == "__main__":
    check_edge_lists()
