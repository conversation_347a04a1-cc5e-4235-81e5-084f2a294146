#!/usr/bin/env python3
"""
脑功能连接数据分析与可视化（简化版）
仅使用FC矩阵进行t-SNE降维可视化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import os
import glob
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FCAnalyzer:
    def __init__(self, data_dir='data'):
        """
        初始化分析器

        Parameters:
        -----------
        data_dir : str
            FC矩阵数据目录
        """
        self.data_dir = data_dir
        self.hc_fc_data = []
        self.mci_fc_data = []
        self.hc_features = []
        self.mci_features = []

        # 指定要分析的脑区索引（基于AAL图谱，从0开始计数）
        self.selected_regions = [7, 8, 23, 24, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 59, 60, 61, 62, 65, 66, 67, 68]

        # 加载脑区名称映射
        self.load_region_names()

    def load_region_names(self):
        """加载脑区名称映射"""
        try:
            import pandas as pd
            df = pd.read_csv('aal_brain_regions.csv')
            self.region_names = dict(zip(df['Index'], df['Region_Name']))

            # 打印选择的脑区信息
            print("选择的脑区:")
            for idx in self.selected_regions:
                if idx in self.region_names:
                    print(f"  {idx}: {self.region_names[idx]}")
                else:
                    print(f"  {idx}: 未知脑区")
        except Exception as e:
            print(f"加载脑区名称时出错: {e}")
            self.region_names = {}

    def load_fc_data(self):
        """加载HC和MCI组的FC矩阵数据"""
        print("正在加载FC矩阵数据...")
        
        # 加载HC组FC矩阵
        hc_fc_files = glob.glob(os.path.join(self.data_dir, 'HC', 'ROICorrelation_FisherZ_*.txt'))
        for file in sorted(hc_fc_files):
            try:
                fc_matrix = np.loadtxt(file)
                if fc_matrix.shape == (116, 116):
                    self.hc_fc_data.append(fc_matrix)
                else:
                    print(f"警告: 文件 {file} 的矩阵尺寸不正确: {fc_matrix.shape}")
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        # 加载MCI组FC矩阵
        mci_fc_files = glob.glob(os.path.join(self.data_dir, 'MCI', 'ROICorrelation_FisherZ_*.txt'))
        for file in sorted(mci_fc_files):
            try:
                fc_matrix = np.loadtxt(file)
                if fc_matrix.shape == (116, 116):
                    self.mci_fc_data.append(fc_matrix)
                else:
                    print(f"警告: 文件 {file} 的矩阵尺寸不正确: {fc_matrix.shape}")
            except Exception as e:
                print(f"加载文件 {file} 时出错: {e}")
        
        print(f"成功加载 HC组: {len(self.hc_fc_data)} 个FC矩阵")
        print(f"成功加载 MCI组: {len(self.mci_fc_data)} 个FC矩阵")
    
    def extract_fc_features(self, fc_matrix):
        """
        提取指定脑区间的FC矩阵特征

        Parameters:
        -----------
        fc_matrix : numpy.ndarray
            功能连接矩阵

        Returns:
        --------
        features : numpy.ndarray
            FC特征向量
        """
        # 处理无穷大和NaN值
        fc_matrix_clean = np.copy(fc_matrix)

        # 将对角线设为0（自连接）
        np.fill_diagonal(fc_matrix_clean, 0)

        # 处理无穷大值
        inf_mask = np.isinf(fc_matrix_clean)
        if np.any(inf_mask):
            print(f"发现 {np.sum(inf_mask)} 个无穷大值，将其替换为极值")
            # 将正无穷替换为最大有限值，负无穷替换为最小有限值
            finite_values = fc_matrix_clean[np.isfinite(fc_matrix_clean)]
            if len(finite_values) > 0:
                max_finite = np.max(finite_values)
                min_finite = np.min(finite_values)
                fc_matrix_clean[fc_matrix_clean == np.inf] = max_finite
                fc_matrix_clean[fc_matrix_clean == -np.inf] = min_finite
            else:
                fc_matrix_clean[inf_mask] = 0

        # 处理NaN值
        nan_mask = np.isnan(fc_matrix_clean)
        if np.any(nan_mask):
            print(f"发现 {np.sum(nan_mask)} 个NaN值，将其替换为0")
            fc_matrix_clean[nan_mask] = 0

        # 提取指定脑区的子矩阵
        selected_matrix = fc_matrix_clean[np.ix_(self.selected_regions, self.selected_regions)]

        # 提取上三角矩阵（去除对角线）
        upper_tri_indices = np.triu_indices(selected_matrix.shape[0], k=1)
        fc_features = selected_matrix[upper_tri_indices]

        # 计算网络级别的统计特征
        network_features = []

        # 全局统计特征
        if len(fc_features) > 0:
            network_features.extend([
                np.mean(fc_features),
                np.std(fc_features),
                np.var(fc_features),
                np.min(fc_features),
                np.max(fc_features),
                np.median(fc_features),
                stats.skew(fc_features) if len(fc_features) > 1 else 0,
                stats.kurtosis(fc_features) if len(fc_features) > 1 else 0
            ])
        else:
            network_features.extend([0] * 8)

        # 节点度中心性（基于选定脑区的连接强度）
        node_strengths = np.sum(np.abs(selected_matrix), axis=1)
        if len(node_strengths) > 0:
            network_features.extend([
                np.mean(node_strengths),
                np.std(node_strengths),
                np.max(node_strengths),
                np.min(node_strengths)
            ])
        else:
            network_features.extend([0] * 4)

        # 合并原始FC特征和网络特征
        combined_features = np.concatenate([fc_features, network_features])

        # 最终检查是否还有无穷大或NaN值
        if not np.all(np.isfinite(combined_features)):
            print("警告: 特征中仍有非有限值，将其替换为0")
            combined_features = np.nan_to_num(combined_features, nan=0.0, posinf=0.0, neginf=0.0)

        return combined_features
    
    def process_features(self):
        """处理所有被试的特征"""
        print("正在提取FC特征...")
        
        # 处理HC组特征
        for i, fc_data in enumerate(self.hc_fc_data):
            features = self.extract_fc_features(fc_data)
            self.hc_features.append(features)
        
        # 处理MCI组特征
        for i, fc_data in enumerate(self.mci_fc_data):
            features = self.extract_fc_features(fc_data)
            self.mci_features.append(features)
        
        print(f"HC组特征维度: {len(self.hc_features)} × {len(self.hc_features[0]) if self.hc_features else 0}")
        print(f"MCI组特征维度: {len(self.mci_features)} × {len(self.mci_features[0]) if self.mci_features else 0}")

        # 计算选定脑区间的连接数
        n_regions = len(self.selected_regions)
        n_connections = n_regions * (n_regions - 1) // 2
        print(f"选定脑区数量: {n_regions}")
        print(f"脑区间连接数: {n_connections}")
        print(f"网络特征数: 12")
        print(f"总特征数: {n_connections + 12}")
    
    def perform_tsne_analysis(self, perplexity=30, n_components=2, random_state=42):
        """
        执行t-SNE降维分析
        
        Parameters:
        -----------
        perplexity : int
            t-SNE困惑度参数
        n_components : int
            降维后的维度
        random_state : int
            随机种子
        """
        print("正在进行t-SNE降维分析...")
        
        if len(self.hc_features) == 0 or len(self.mci_features) == 0:
            print("错误: 没有足够的数据进行分析")
            return None, None
        
        # 合并所有特征
        all_features = np.array(self.hc_features + self.mci_features)
        labels = ['HC'] * len(self.hc_features) + ['MCI'] * len(self.mci_features)
        
        print(f"总样本数: {len(all_features)}")
        print(f"特征维度: {all_features.shape[1]}")
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(all_features)
        
        # 先用PCA降维以提高t-SNE效率
        if features_scaled.shape[1] > 50:
            print("使用PCA预处理...")
            pca = PCA(n_components=50, random_state=random_state)
            features_pca = pca.fit_transform(features_scaled)
            print(f"PCA解释方差比: {pca.explained_variance_ratio_.sum():.3f}")
        else:
            features_pca = features_scaled
        
        # 调整困惑度参数
        n_samples = len(all_features)
        perplexity = min(perplexity, (n_samples - 1) // 3)
        print(f"使用困惑度: {perplexity}")
        
        # t-SNE降维
        tsne = TSNE(n_components=n_components, perplexity=perplexity, 
                   random_state=random_state, n_iter=1000, verbose=1)
        features_tsne = tsne.fit_transform(features_pca)
        
        return features_tsne, labels
    
    def visualize_results(self, features_tsne, labels, save_path='results'):
        """
        可视化t-SNE结果
        
        Parameters:
        -----------
        features_tsne : numpy.ndarray
            t-SNE降维后的特征
        labels : list
            样本标签
        save_path : str
            保存路径
        """
        print("正在生成可视化结果...")
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'tSNE1': features_tsne[:, 0],
            'tSNE2': features_tsne[:, 1],
            'Group': labels
        })
        
        # 设置图形样式
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 子图1: 基本散点图
        colors = {'HC': '#2E86AB', 'MCI': '#F24236'}
        for group in ['HC', 'MCI']:
            group_data = df[df['Group'] == group]
            axes[0,0].scatter(group_data['tSNE1'], group_data['tSNE2'], 
                          c=colors[group], label=group, alpha=0.7, s=50)
        
        axes[0,0].set_xlabel('t-SNE Component 1', fontsize=12)
        axes[0,0].set_ylabel('t-SNE Component 2', fontsize=12)
        axes[0,0].set_title('HC vs MCI Groups - Selected Brain Regions FC', fontsize=14)
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)

        # 子图2: 密度图
        sns.scatterplot(data=df, x='tSNE1', y='tSNE2', hue='Group',
                       palette=colors, alpha=0.7, s=50, ax=axes[0,1])
        axes[0,1].set_title('HC vs MCI Groups - Selected Regions', fontsize=14)
        axes[0,1].grid(True, alpha=0.3)
        
        # 子图3: 分布直方图
        for i, group in enumerate(['HC', 'MCI']):
            group_data = df[df['Group'] == group]
            axes[1,0].hist(group_data['tSNE1'], alpha=0.6, label=f'{group} (tSNE1)', 
                          color=colors[group], bins=20)
        axes[1,0].set_xlabel('t-SNE Component 1')
        axes[1,0].set_ylabel('Frequency')
        axes[1,0].set_title('Distribution of t-SNE Component 1')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        
        # 子图4: 箱线图
        df_melted = pd.melt(df, id_vars=['Group'], value_vars=['tSNE1', 'tSNE2'], 
                           var_name='Component', value_name='Value')
        sns.boxplot(data=df_melted, x='Component', y='Value', hue='Group', 
                   palette=colors, ax=axes[1,1])
        axes[1,1].set_title('Box Plot of t-SNE Components')
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'fc_tsne_visualization.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        # 保存数据
        df.to_csv(os.path.join(save_path, 'fc_tsne_results.csv'), index=False)

        # 保存选定脑区信息
        self.save_selected_regions_info(save_path)
        
        # 计算组间距离统计
        hc_data = df[df['Group'] == 'HC'][['tSNE1', 'tSNE2']].values
        mci_data = df[df['Group'] == 'MCI'][['tSNE1', 'tSNE2']].values
        
        hc_center = np.mean(hc_data, axis=0)
        mci_center = np.mean(mci_data, axis=0)
        center_distance = np.linalg.norm(hc_center - mci_center)
        
        print(f"\n=== 分析结果统计 ===")
        print(f"HC组样本数: {len(hc_data)}")
        print(f"MCI组样本数: {len(mci_data)}")
        print(f"HC组中心: ({hc_center[0]:.3f}, {hc_center[1]:.3f})")
        print(f"MCI组中心: ({mci_center[0]:.3f}, {mci_center[1]:.3f})")
        print(f"组间中心距离: {center_distance:.3f}")
        print(f"结果已保存到 {save_path} 目录")
        
        return df

    def save_selected_regions_info(self, save_path):
        """保存选定脑区的详细信息"""
        try:
            import pandas as pd

            # 创建脑区信息DataFrame
            region_info = []
            for idx in self.selected_regions:
                region_name = self.region_names.get(idx, f"Unknown_Region_{idx}")
                region_info.append({
                    'Index': idx,
                    'Region_Name': region_name
                })

            df_regions = pd.DataFrame(region_info)
            df_regions.to_csv(os.path.join(save_path, 'selected_brain_regions.csv'), index=False)

            # 保存连接矩阵信息
            n_regions = len(self.selected_regions)
            connection_info = []
            for i in range(n_regions):
                for j in range(i+1, n_regions):
                    idx1, idx2 = self.selected_regions[i], self.selected_regions[j]
                    name1 = self.region_names.get(idx1, f"Unknown_{idx1}")
                    name2 = self.region_names.get(idx2, f"Unknown_{idx2}")
                    connection_info.append({
                        'Region1_Index': idx1,
                        'Region1_Name': name1,
                        'Region2_Index': idx2,
                        'Region2_Name': name2,
                        'Connection': f"{name1} - {name2}"
                    })

            df_connections = pd.DataFrame(connection_info)
            df_connections.to_csv(os.path.join(save_path, 'brain_region_connections.csv'), index=False)

            print(f"脑区信息已保存到: {os.path.join(save_path, 'selected_brain_regions.csv')}")
            print(f"连接信息已保存到: {os.path.join(save_path, 'brain_region_connections.csv')}")

        except Exception as e:
            print(f"保存脑区信息时出错: {e}")

def main():
    """主函数"""
    print("=== 脑功能连接数据分析与可视化（特定脑区版） ===")

    # 初始化分析器
    analyzer = FCAnalyzer()
    
    # 加载数据
    analyzer.load_fc_data()
    
    # 处理特征
    analyzer.process_features()
    
    # t-SNE分析
    features_tsne, labels = analyzer.perform_tsne_analysis()
    
    if features_tsne is not None:
        # 可视化结果
        results_df = analyzer.visualize_results(features_tsne, labels)
        print("分析完成！")
    else:
        print("分析失败，请检查数据。")

if __name__ == "__main__":
    main()
